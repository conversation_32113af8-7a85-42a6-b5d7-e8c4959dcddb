package ut.com.fenqile.svip.api.service.logic.oa;

import com.fenqile.cf.mall.domain.order.dto.CfMallOrderDto;
import com.fenqile.svip.api.bean.request.oa.QueryUserVipOaInfoReq;
import com.fenqile.svip.api.bean.response.oa.QueryUserVipOaInfoResp;
import com.fenqile.svip.api.service.logic.oa.VipOaLogic;
import com.fenqile.svip.api.service.proxy.info.SkuVipQueryProxy;
import com.fenqile.svip.api.service.proxy.mall.CfMallOrderProxy;
import com.fenqile.svip.api.service.proxy.order.SvipOrderProxy;
import com.fenqile.svip.api.service.proxy.user.UserOaProxy;
import com.fenqile.svip.config.bean.vo.SkuVo;
import com.fenqile.svip.order.bean.vo.VipOrderVo;
import com.fenqile.svip.user.bean.UserVipRecordVo;
import com.fenqile.svip.user.bean.request.QueryUserVipInfoListReq;
import com.fenqile.svip.user.bean.response.QueryUserVipInfoListResp;
import com.github.pagehelper.PageInfo;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;
import static org.testng.Assert.*;

/**
 * VipOaLogic单元测试
 */
public class VipOaLogicTest {

    @InjectMocks
    private VipOaLogic vipOaLogic;

    @Mock
    private SvipOrderProxy svipOrderProxy;

    @Mock
    private CfMallOrderProxy cfMallOrderProxy;

    @Mock
    private SkuVipQueryProxy vipQueryProxy;

    @Mock
    private UserOaProxy userOaProxy;

    @BeforeMethod
    public void setUp() {
        initMocks(this);
    }

    @Test
    public void testQueryUserVipInfo_withOrderId() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);
        req.setOrderId("ORDER123");

        VipOrderVo vipOrderVo = new VipOrderVo();
        vipOrderVo.setOrderId("ORDER123");
        vipOrderVo.setSkuId("SKU123");

        UserVipRecordVo userVipRecord = new UserVipRecordVo();
        userVipRecord.setOrderId("ORDER123");
        userVipRecord.setSkuId("SKU123");

        QueryUserVipInfoListResp vipInfoListResp = new QueryUserVipInfoListResp();
        vipInfoListResp.setRecordVos(Collections.singletonList(userVipRecord));

        SkuVo skuVo = new SkuVo();
        skuVo.setName("测试SKU");

        // Mock依赖调用
        when(svipOrderProxy.queryOrderInfoByOrderId(anyString())).thenReturn(vipOrderVo);
        when(cfMallOrderProxy.queryCfMallOrderInfo(anyString())).thenReturn(null);
        when(userOaProxy.queryUserVipInfoList(any(QueryUserVipInfoListReq.class))).thenReturn(vipInfoListResp);
        when(vipQueryProxy.querySkuVoBySkuId(anyString(), any(Boolean.class))).thenReturn(skuVo);

        // 执行测试
        QueryUserVipOaInfoResp result = vipOaLogic.queryUserVipInfo(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getTotal(), Integer.valueOf(1));
        assertNotNull(result.getOrderList());
        assertEquals(result.getOrderList().size(), 1);
        assertEquals(result.getOrderList().get(0).getOrderId(), "ORDER123");
    }

    @Test
    public void testQueryUserVipInfo_batchQuery() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);
        req.setOffset(0);
        req.setLimit(10);

        VipOrderVo vipOrderVo = new VipOrderVo();
        vipOrderVo.setOrderId("ORDER123");
        vipOrderVo.setSkuId("SKU123");

        PageInfo<VipOrderVo> vipOrderPageInfo = new PageInfo<>();
        vipOrderPageInfo.setList(Collections.singletonList(vipOrderVo));
        vipOrderPageInfo.setTotal(1);

        UserVipRecordVo userVipRecord = new UserVipRecordVo();
        userVipRecord.setOrderId("ORDER123");
        userVipRecord.setSkuId("SKU123");

        QueryUserVipInfoListResp vipInfoListResp = new QueryUserVipInfoListResp();
        vipInfoListResp.setRecordVos(Collections.singletonList(userVipRecord));

        // Mock依赖调用
        when(svipOrderProxy.queryVipOrderInfoWithPage(any())).thenReturn(vipOrderPageInfo);
        when(userOaProxy.queryUserVipInfoList(any(QueryUserVipInfoListReq.class))).thenReturn(vipInfoListResp);
        when(cfMallOrderProxy.queryCfMallOrderList(any())).thenReturn(null);

        // 执行测试
        QueryUserVipOaInfoResp result = vipOaLogic.queryUserVipInfo(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getTotal(), Integer.valueOf(1));
        assertNotNull(result.getOrderList());
        assertEquals(result.getOrderList().size(), 1);
    }

    @Test
    public void testQueryUserVipInfo_noData() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);

        // Mock返回空数据
        when(svipOrderProxy.queryVipOrderInfoWithPage(any())).thenReturn(null);
        when(cfMallOrderProxy.queryCfMallOrderList(any())).thenReturn(null);

        // 执行测试
        QueryUserVipOaInfoResp result = vipOaLogic.queryUserVipInfo(req);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testBuildVipOaOrderInfoDetail_vipOrder() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);

        VipOrderVo vipOrderVo = new VipOrderVo();
        vipOrderVo.setOrderId("ORDER123");
        vipOrderVo.setSkuId("SKU123");
        vipOrderVo.setCreateTime(new Date());

        UserVipRecordVo vipRecordVo = new UserVipRecordVo();
        vipRecordVo.setOrderId("ORDER123");
        vipRecordVo.setSkuId("SKU123");

        SkuVo skuVo = new SkuVo();
        skuVo.setName("测试SKU");

        // Mock依赖调用
        when(vipQueryProxy.querySkuVoBySkuId(anyString(), any(Boolean.class))).thenReturn(skuVo);

        // 执行测试
        QueryUserVipOaInfoResp.VipOrderInfoDetail result = vipOaLogic.buildVipOaOrderInfoDetail(req, vipOrderVo, null, vipRecordVo);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getOrderId(), "ORDER123");
        assertEquals(result.getSkuId(), "SKU123");
        assertEquals(result.getOrderVersion(), Integer.valueOf(1)); // SVIP_ORDER_VERSION
        assertNotNull(result.getVipDetail());
    }

    @Test
    public void testBuildVipOaOrderInfoDetail_mallOrder() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);

        CfMallOrderDto cfMallOrderDto = new CfMallOrderDto();
        cfMallOrderDto.setOrderId("ORDER123");
        cfMallOrderDto.setProductId("PRODUCT123");
        cfMallOrderDto.setCreateTime(new Date());

        UserVipRecordVo vipRecordVo = new UserVipRecordVo();
        vipRecordVo.setOrderId("ORDER123");
        vipRecordVo.setSkuId("SKU123");

        SkuVo skuVo = new SkuVo();
        skuVo.setName("测试SKU");

        // Mock依赖调用
        when(vipQueryProxy.querySkuVoBySkuId(anyString(), any(Boolean.class))).thenReturn(skuVo);

        // 执行测试
        QueryUserVipOaInfoResp.VipOrderInfoDetail result = vipOaLogic.buildVipOaOrderInfoDetail(req, null, cfMallOrderDto, vipRecordVo);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getOrderId(), "ORDER123");
        assertEquals(result.getProductId(), "PRODUCT123");
        assertEquals(result.getOrderVersion(), Integer.valueOf(2)); // MALL_ORDER_VERSION
        assertNotNull(result.getVipDetail());
    }

    @Test
    public void testBuildVipOaOrderInfoDetail_noOrder() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);

        // 执行测试 - 传入null订单
        QueryUserVipOaInfoResp.VipOrderInfoDetail result = vipOaLogic.buildVipOaOrderInfoDetail(req, null, null, null);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testBuildVipOaOrderInfoDetail_noVipRecord() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);

        VipOrderVo vipOrderVo = new VipOrderVo();
        vipOrderVo.setOrderId("ORDER123");
        vipOrderVo.setSkuId("SKU123");
        vipOrderVo.setCreateTime(new Date());

        // 执行测试 - 不传入会员记录
        QueryUserVipOaInfoResp.VipOrderInfoDetail result = vipOaLogic.buildVipOaOrderInfoDetail(req, vipOrderVo, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getOrderId(), "ORDER123");
        assertEquals(result.getSkuId(), "SKU123");
        assertNull(result.getVipDetail()); // 没有会员记录时VipDetail为null
    }

    @Test
    public void testBatchQueryOrderVipInfo_onlyMallData() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);
        req.setOffset(0);
        req.setLimit(10);

        CfMallOrderDto cfMallOrderDto = new CfMallOrderDto();
        cfMallOrderDto.setOrderId("MALL_ORDER123");
        cfMallOrderDto.setProductId("PRODUCT123");
        cfMallOrderDto.setCreateTime(new Date());

        PageInfo<CfMallOrderDto> cfMallOrderPageInfo = new PageInfo<>();
        cfMallOrderPageInfo.setList(Collections.singletonList(cfMallOrderDto));
        cfMallOrderPageInfo.setTotal(1);

        UserVipRecordVo userVipRecord = new UserVipRecordVo();
        userVipRecord.setOrderId("MALL_ORDER123");
        userVipRecord.setSkuId("SKU123");

        QueryUserVipInfoListResp vipInfoListResp = new QueryUserVipInfoListResp();
        vipInfoListResp.setRecordVos(Collections.singletonList(userVipRecord));

        // Mock依赖调用 - svip数据为空，商城数据不为空
        when(svipOrderProxy.queryVipOrderInfoWithPage(any())).thenReturn(null);
        when(cfMallOrderProxy.queryCfMallOrderList(any())).thenReturn(cfMallOrderPageInfo);
        when(userOaProxy.queryUserVipInfoList(any(QueryUserVipInfoListReq.class))).thenReturn(vipInfoListResp);

        // 执行测试
        QueryUserVipOaInfoResp result = vipOaLogic.batchQueryOrderVipInfo(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getTotal(), Integer.valueOf(1));
        assertNotNull(result.getOrderList());
        assertEquals(result.getOrderList().size(), 1);
        assertEquals(result.getOrderList().get(0).getOrderId(), "MALL_ORDER123");
    }

    @Test
    public void testBatchQueryOrderVipInfo_mixedData() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);
        req.setOffset(0);
        req.setLimit(10);

        // SVIP订单数据
        VipOrderVo vipOrderVo = new VipOrderVo();
        vipOrderVo.setOrderId("VIP_ORDER123");
        vipOrderVo.setSkuId("SKU123");
        vipOrderVo.setCreateTime(new Date(System.currentTimeMillis() - 1000)); // 较早时间

        PageInfo<VipOrderVo> vipOrderPageInfo = new PageInfo<>();
        vipOrderPageInfo.setList(Collections.singletonList(vipOrderVo));
        vipOrderPageInfo.setTotal(1);

        // 商城订单数据
        CfMallOrderDto cfMallOrderDto = new CfMallOrderDto();
        cfMallOrderDto.setOrderId("MALL_ORDER123");
        cfMallOrderDto.setProductId("PRODUCT123");
        cfMallOrderDto.setCreateTime(new Date()); // 较晚时间

        PageInfo<CfMallOrderDto> cfMallOrderPageInfo = new PageInfo<>();
        cfMallOrderPageInfo.setList(Collections.singletonList(cfMallOrderDto));
        cfMallOrderPageInfo.setTotal(1);

        UserVipRecordVo userVipRecord1 = new UserVipRecordVo();
        userVipRecord1.setOrderId("VIP_ORDER123");
        userVipRecord1.setSkuId("SKU123");

        UserVipRecordVo userVipRecord2 = new UserVipRecordVo();
        userVipRecord2.setOrderId("MALL_ORDER123");
        userVipRecord2.setSkuId("SKU456");

        QueryUserVipInfoListResp vipInfoListResp = new QueryUserVipInfoListResp();
        vipInfoListResp.setRecordVos(Arrays.asList(userVipRecord1, userVipRecord2));

        // Mock依赖调用 - 两种数据都不为空
        when(svipOrderProxy.queryVipOrderInfoWithPage(any())).thenReturn(vipOrderPageInfo);
        when(cfMallOrderProxy.queryCfMallOrderList(any())).thenReturn(cfMallOrderPageInfo);
        when(userOaProxy.queryUserVipInfoList(any(QueryUserVipInfoListReq.class))).thenReturn(vipInfoListResp);

        // 执行测试
        QueryUserVipOaInfoResp result = vipOaLogic.batchQueryOrderVipInfo(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getTotal(), Integer.valueOf(2)); // 合并后总数为2
        assertNotNull(result.getOrderList());
        assertEquals(result.getOrderList().size(), 2);
        // 验证按创建时间倒序排序 - 较晚的订单排在前面
        assertEquals(result.getOrderList().get(0).getOrderId(), "MALL_ORDER123");
        assertEquals(result.getOrderList().get(1).getOrderId(), "VIP_ORDER123");
    }

    @Test
    public void testQueryUserVipInfo_orderNotFound() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);
        req.setOrderId("NOT_EXIST_ORDER");

        // Mock返回空数据
        when(svipOrderProxy.queryOrderInfoByOrderId(anyString())).thenReturn(null);
        when(cfMallOrderProxy.queryCfMallOrderInfo(anyString())).thenReturn(null);
        when(userOaProxy.queryUserVipInfoList(any(QueryUserVipInfoListReq.class))).thenReturn(null);

        // 执行测试
        QueryUserVipOaInfoResp result = vipOaLogic.queryUserVipInfo(req);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testBuildVipOaOrderInfoDetail_withPrivilegeQuery() {
        // 准备测试数据
        QueryUserVipOaInfoReq req = new QueryUserVipOaInfoReq();
        req.setUid(12345);
        req.setQueryPrivilegeInfo(true); // 查询权益信息

        VipOrderVo vipOrderVo = new VipOrderVo();
        vipOrderVo.setOrderId("ORDER123");
        vipOrderVo.setSkuId("SKU123");
        vipOrderVo.setCreateTime(new Date());

        UserVipRecordVo vipRecordVo = new UserVipRecordVo();
        vipRecordVo.setOrderId("ORDER123");
        vipRecordVo.setSkuId("SKU123");

        SkuVo skuVo = new SkuVo();
        skuVo.setName("测试SKU");

        // Mock依赖调用
        when(vipQueryProxy.querySkuVoBySkuId(anyString(), any(Boolean.class))).thenReturn(skuVo);

        // 执行测试
        QueryUserVipOaInfoResp.VipOrderInfoDetail result = vipOaLogic.buildVipOaOrderInfoDetail(req, vipOrderVo, null, vipRecordVo);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.getOrderId(), "ORDER123");
        assertNotNull(result.getVipDetail());
        assertNotNull(result.getVipDetail().getSkuVo());
        assertEquals(result.getVipDetail().getSkuVo().getName(), "测试SKU");
    }
}
